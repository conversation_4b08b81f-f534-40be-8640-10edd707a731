# MDM系统功能架构展示

## 系统概述

MDM（主数据管理）系统是一个基于Vue 3 + Element Plus的前端应用，采用标准的Admin布局设计，集成后端API实现完整的数据管理流程。

## 核心业务流程

### 1. 数据加载器 (Data Loader) - 5步流程

```
文件上传 → 选择类型 → 数据校验 → 数据处理 → 完成
```

#### 技术实现架构：

**前端组件层：**
- `src/views/DataLoader.vue` - 主界面组件
- `src/stores/dataLoader.js` - 状态管理
- `src/api/dataLoader.js` - API接口层

**核心功能实现：**

1. **文件上传模块**
   - 使用 Element Plus Upload 组件
   - 支持拖拽上传和点击上传
   - 文件格式验证（Excel、CSV）
   - 文件大小限制（10MB）

2. **操作类型选择**
   - 实体类型选择：创建(product) / 更新(hco)
   - 单选按钮组件实现
   - 表单验证机制

3. **数据校验引擎**
   - Mock校验逻辑：文件名包含"error"或"fail"时模拟失败
   - 校验状态管理：validating、validationResult
   - 错误报告生成和下载功能
   - 重新上传机制

4. **数据处理流程**
   - 异步任务处理
   - 实时进度轮询（每2秒）
   - 状态流转：pending → processing → completed/failed

5. **结果展示**
   - 成功/失败状态展示
   - 处理统计信息
   - 错误记录下载

#### API接口设计：

```javascript
// 数据校验接口
POST /api/v1/data-loader/validate
Content-Type: multipart/form-data
参数: file, entity_type

// 文件上传接口
POST /data-loader/upload
参数: file, entity_type

// 任务状态查询
GET /data-loader/tasks/{task_id}
GET /data-loader/tasks/{task_id}/progress
```

### 2. 外部匹配 (External Match) - 6步流程

```
文件上传 → 文件确认 → 处理进度 → 结果审核 → 数据下发 → 完成
```

#### 技术实现架构：

**前端组件层：**
- `src/views/ExternalMatch.vue` - 主界面组件
- `src/stores/externalMatch.js` - 状态管理
- `src/api/externalMatch.js` - API接口层

**核心功能实现：**

1. **文件上传与确认**
   - 文件信息展示（名称、大小、类型、时间）
   - 数据摘要预览（记录数、列数估算）
   - 文件路径显示

2. **智能匹配处理**
   - 后端异步处理模拟
   - 状态流转：UPLOADED → PROCESSING → MATCHED → DISTRIBUTING → COMPLETED
   - 进度条实时更新（每2-3秒递增）

3. **结果审核机制**
   - 匹配结果统计展示
   - 成功/失败记录分类
   - 质量评估指标

4. **数据分发系统**
   - 多系统分发支持
   - 分发进度监控
   - 失败记录处理

5. **完成状态管理**
   - 最终结果汇总
   - 日志记录下载
   - 新任务创建入口

#### API接口设计：

```javascript
// 文件上传
POST /external-match/upload

// 确认并开始匹配
POST /external-match/tasks/{task_id}/confirm

// 进度查询
GET /external-match/tasks/{task_id}/progress

// 开始分发
POST /external-match/tasks/{task_id}/distribute

// 分发进度
GET /external-match/tasks/{task_id}/distribution-progress

// 下载错误记录
GET /external-match/tasks/{task_id}/download-errors
```

### 3. 用户管理 (User Management)

#### 技术实现架构：

**前端组件层：**
- `src/views/UserManagement.vue` - 用户管理界面
- `src/stores/user.js` - 用户状态管理
- `src/api/users.js` - 用户API接口

**核心功能实现：**

1. **用户CRUD操作**
   - 用户列表展示（表格组件）
   - 新增用户对话框
   - 编辑用户信息
   - 删除用户确认

2. **角色权限管理**
   - 角色枚举：admin（管理员）、user（普通用户）、viewer（查看者）
   - 角色显示标签化
   - 权限验证机制

3. **搜索过滤功能**
   - 用户名搜索
   - 角色筛选
   - 实时过滤结果

4. **表单验证**
   - 用户名唯一性验证
   - 邮箱格式验证
   - 密码强度要求

#### API接口设计：

```javascript
// 用户管理接口
GET /users/          // 获取用户列表
POST /users/         // 创建用户
PUT /users/{user_id} // 更新用户
DELETE /users/{user_id} // 删除用户
```

## 系统架构特点

### 1. 前端架构设计

**技术栈：**
- Vue 3 + Composition API
- Element Plus UI组件库
- Pinia状态管理
- Vue Router路由管理
- Axios HTTP客户端

**架构模式：**
- 标准Admin布局（左侧边栏 + 顶部导航 + 右侧内容区）
- 组件化开发
- 状态集中管理
- API服务层分离

### 2. 状态管理架构

**Store模块化：**
- `authStore` - 认证状态管理
- `userStore` - 用户管理状态
- `dataLoaderStore` - 数据加载器状态
- `externalMatchStore` - 外部匹配状态

**状态流转：**
- 响应式数据绑定
- 异步操作处理
- 错误状态管理
- 加载状态控制

### 3. API集成架构

**HTTP客户端配置：**
- 统一的请求/响应拦截器
- 错误处理机制
- 认证token管理
- 请求超时控制

**API模块化：**
- 按功能模块分离API服务
- 统一的响应格式处理
- Mock数据支持
- 环境配置管理

### 4. 用户体验设计

**交互设计：**
- 步骤式流程引导
- 实时进度反馈
- 错误提示和恢复
- 响应式布局适配

**性能优化：**
- 组件懒加载
- 数据分页处理
- 文件上传进度显示
- 轮询优化机制

## 部署和运行

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev -- --port 3001

# 访问地址
http://localhost:3001
```

### 后端集成
- 后端API地址：http://localhost:8000
- API文档：http://localhost:8000/docs
- 支持跨域请求配置
- 统一错误码处理

## 技术亮点

1. **模块化架构** - 清晰的代码组织和职责分离
2. **状态管理** - 集中式状态管理，数据流向清晰
3. **API设计** - RESTful API设计，支持Mock和真实数据切换
4. **用户体验** - 流程化操作，实时反馈，错误恢复
5. **可扩展性** - 组件化设计，易于功能扩展和维护
6. **国际化支持** - 全中文界面，符合本土化需求

这个架构展示了MDM系统如何通过现代前端技术栈实现复杂的数据管理业务流程，为用户提供直观、高效的操作体验。
